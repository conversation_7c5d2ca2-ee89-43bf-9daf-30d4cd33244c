-- variables.lua - Shared variables for Air780EG firmware
-- This module contains variables that can be shared across different files
-- Adapted from AIR720BLE variables.lua for AIR780 compatibility

local variables = {}

-- Relay and output states
variables.relay1_state = 0
variables.relay2_state = 0
variables.relay3_state = 0
variables.key1_state = 0
variables.key2_state = 0
variables.keypower_state = 0

-- Communication flags
variables.upload_flag = false
variables.upload_switch = true
variables.mqtt_data = nil
variables.sms_data = nil

-- Phone numbers for SMS functionality
variables.phone_number1 = nil
variables.phone_number2 = nil
variables.phone_number3 = nil
variables.phone_number4 = 1411  -- Default service number
variables.callback_number = nil

-- System states
variables.alarm_state = false
variables.key_state = false
variables.asa_state = false
variables.as_wait_s2_falling = true
variables.sound_flag = true
variables.gps_flag = true

-- Timer and scheduling
variables.timers_queue = {}
variables.currentTime = 0

-- Command processing
variables.commandCounter = {}
variables.triggerCount = 3

-- Device configuration
variables.device_name = "aslaa780"  -- Device identifier
variables.carAlreadyStarted = false
variables.isLicensed = true

-- Voltage monitoring
variables.voltage_offset = 0  -- Voltage calibration offset
variables.voltage_threshold = 0.5  -- Threshold for voltage change notifications
variables.voltage_notify_flag = false  -- Enable/disable voltage notifications

-- Auto-shutdown feature
variables.auto_shutdown_enabled = true
variables.auto_shutdown_timer_id = nil
variables.auto_shutdown_minutes = 30  -- Default: 30 minutes
variables.auto_shutdown_time = variables.auto_shutdown_minutes * 60 * 1000  -- Convert to milliseconds
variables.last_as_command_time = 0

-- Vehicle-specific settings
variables.geely_atlas_mode = false  -- Special mode for specific vehicles

-- GPS monitoring
variables.gps_warned = false  -- Flag to track GPS warning status
variables.last_gps_status = false  -- Track last known GPS status
variables.gps_module_warning_shown = false  -- Avoid repeated warnings

-- Logging and diagnostics
variables.log_level = 2  -- Default log level (INFO)
variables.diagnostics_enabled = true
variables.log_to_file = true

-- Command timing settings (all values in milliseconds)
variables.lock_init_duration = 2000      -- Duration to wait for key initialization
variables.lock_press_duration = 1000      -- Duration to hold lock button
variables.unlock_press_duration = 1000    -- Duration to hold unlock button
variables.lock_wait_duration = 2000       -- Wait before pressing lock button
variables.unlock_wait_duration = 1000     -- Wait before pressing unlock button
variables.between_press_duration = 1000   -- Duration between multiple button presses
variables.remote_start_duration = 4000    -- Duration to hold remote start button
variables.mirror_duration = 3000          -- Duration for mirror operation
variables.relay1_on_duration = 3000       -- Duration to keep Relay1 on
variables.relay2_on_duration = 3000       -- Duration to keep Relay2 on

-- Air780-specific sensor settings
variables.shtc3_temp_offset = 0  -- Temperature calibration offset for SHTC3
variables.adc_scaling_factor = 16.14  -- ADC voltage scaling factor
variables.sensor_read_interval = 5000  -- Sensor reading interval in ms
variables.mqtt_publish_interval = 60000  -- MQTT publish interval in ms

-- Network and connectivity
variables.mqtt_server = "elec.mn"
variables.mqtt_port = 1883
variables.mqtt_username = "admin"
variables.mqtt_password = "public"
variables.network_retry_count = 0
variables.max_network_retries = 5

return variables
