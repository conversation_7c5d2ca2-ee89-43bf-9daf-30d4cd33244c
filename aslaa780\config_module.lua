-- config_module.lua - Configuration management for Air780EG
-- Handles loading and saving of device configuration settings

local ConfigModule = {}

-- Import required modules
local vars = require('variables')
local my_utils = require('my_utils')

-- Initialize log module if not available
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO][" .. tag .. "]", ...) end,
        warn = function(tag, ...) print("[WARN][" .. tag .. "]", ...) end,
        error = function(tag, ...) print("[ERROR][" .. tag .. "]", ...) end,
        debug = function(tag, ...) print("[DEBUG][" .. tag .. "]", ...) end
    }
end

-- Configuration file paths
local CONFIG_DIR = "/user_dir"
local CONFIG_FILES = {
    phone1 = CONFIG_DIR .. "/phone1.txt",
    phone2 = CONFIG_DIR .. "/phone2.txt",
    phone3 = CONFIG_DIR .. "/phone3.txt",
    volt_offset = CONFIG_DIR .. "/volt_offset.txt",
    volt_threshold = CONFIG_DIR .. "/volt_threshold.txt",
    volt_notify = CONFIG_DIR .. "/volt_notify.txt",
    alarm = CONFIG_DIR .. "/alarm.txt",
    sound_flag = CONFIG_DIR .. "/sound_flag.txt",
    gps_flag = CONFIG_DIR .. "/gps_flag.txt",
    license = CONFIG_DIR .. "/license.txt",
    geely_mode = CONFIG_DIR .. "/geely_mode.txt",
    auto_shutdown = CONFIG_DIR .. "/auto_shutdown.txt",
    auto_shutdown_time = CONFIG_DIR .. "/auto_shutdown_time.txt",
    device_name = CONFIG_DIR .. "/device_name.txt",
    mqtt_server = CONFIG_DIR .. "/mqtt_server.txt",
    mqtt_port = CONFIG_DIR .. "/mqtt_port.txt",
    mqtt_username = CONFIG_DIR .. "/mqtt_username.txt",
    mqtt_password = CONFIG_DIR .. "/mqtt_password.txt"
}

-- Default configuration values
local DEFAULT_CONFIG = {
    phone1 = nil,
    phone2 = nil,
    phone3 = nil,
    volt_offset = 0,
    volt_threshold = 0.5,
    volt_notify = false,
    alarm = false,
    sound_flag = true,
    gps_flag = true,
    license = true,
    geely_mode = false,
    auto_shutdown = true,
    auto_shutdown_time = 30,
    device_name = "aslaa780",
    mqtt_server = "elec.mn",
    mqtt_port = 1883,
    mqtt_username = "admin",
    mqtt_password = "public"
}

-- Initialize configuration module
function ConfigModule.init()
    log.info("ConfigModule", "Initializing configuration module...")
    
    -- Create configuration directory
    local dirCreated = my_utils.createDirectory(CONFIG_DIR)
    if not dirCreated then
        log.warn("ConfigModule", "Could not create config directory, but will attempt to use it anyway")
    end
    
    -- Load all configuration
    ConfigModule.loadAll()
    
    log.info("ConfigModule", "Configuration module initialized")
    return true
end

-- Load all configuration from files
function ConfigModule.loadAll()
    log.info("ConfigModule", "Loading all configuration...")
    
    -- Load phone numbers
    vars.phone_number1 = ConfigModule.loadString("phone1")
    vars.phone_number2 = ConfigModule.loadString("phone2")
    vars.phone_number3 = ConfigModule.loadString("phone3")
    
    -- Load voltage settings
    vars.voltage_offset = ConfigModule.loadNumber("volt_offset", DEFAULT_CONFIG.volt_offset)
    vars.voltage_threshold = ConfigModule.loadNumber("volt_threshold", DEFAULT_CONFIG.volt_threshold)
    vars.voltage_notify_flag = ConfigModule.loadBoolean("volt_notify", DEFAULT_CONFIG.volt_notify)
    
    -- Load system flags
    vars.alarm_state = ConfigModule.loadBoolean("alarm", DEFAULT_CONFIG.alarm)
    vars.sound_flag = ConfigModule.loadBoolean("sound_flag", DEFAULT_CONFIG.sound_flag)
    vars.gps_flag = ConfigModule.loadBoolean("gps_flag", DEFAULT_CONFIG.gps_flag)
    vars.isLicensed = ConfigModule.loadBoolean("license", DEFAULT_CONFIG.license)
    vars.geely_atlas_mode = ConfigModule.loadBoolean("geely_mode", DEFAULT_CONFIG.geely_mode)
    
    -- Load auto-shutdown settings
    vars.auto_shutdown_enabled = ConfigModule.loadBoolean("auto_shutdown", DEFAULT_CONFIG.auto_shutdown)
    vars.auto_shutdown_minutes = ConfigModule.loadNumber("auto_shutdown_time", DEFAULT_CONFIG.auto_shutdown_time)
    vars.auto_shutdown_time = vars.auto_shutdown_minutes * 60 * 1000  -- Convert to milliseconds
    
    -- Load device settings
    vars.device_name = ConfigModule.loadString("device_name") or DEFAULT_CONFIG.device_name
    
    -- Load MQTT settings
    vars.mqtt_server = ConfigModule.loadString("mqtt_server") or DEFAULT_CONFIG.mqtt_server
    vars.mqtt_port = ConfigModule.loadNumber("mqtt_port", DEFAULT_CONFIG.mqtt_port)
    vars.mqtt_username = ConfigModule.loadString("mqtt_username") or DEFAULT_CONFIG.mqtt_username
    vars.mqtt_password = ConfigModule.loadString("mqtt_password") or DEFAULT_CONFIG.mqtt_password
    
    log.info("ConfigModule", "Configuration loading complete")
end

-- Load a string value from file
function ConfigModule.loadString(key)
    local filepath = CONFIG_FILES[key]
    if not filepath then
        log.error("ConfigModule", "Unknown config key: " .. key)
        return nil
    end
    
    if my_utils.fileExists(filepath) then
        local content = my_utils.readFile(filepath)
        if content then
            content = content:match("^%s*(.-)%s*$")  -- Trim whitespace
            log.info("ConfigModule", "Loaded " .. key .. ": " .. content)
            return content
        end
    end
    
    return nil
end

-- Load a number value from file
function ConfigModule.loadNumber(key, default_value)
    local content = ConfigModule.loadString(key)
    if content then
        local number = tonumber(content)
        if number then
            return number
        else
            log.warn("ConfigModule", "Invalid number format for " .. key .. ": " .. content)
        end
    end
    
    return default_value or 0
end

-- Load a boolean value from file
function ConfigModule.loadBoolean(key, default_value)
    local content = ConfigModule.loadString(key)
    if content then
        return content:lower() == "true"
    end
    
    return default_value or false
end

-- Save a string value to file
function ConfigModule.saveString(key, value)
    local filepath = CONFIG_FILES[key]
    if not filepath then
        log.error("ConfigModule", "Unknown config key: " .. key)
        return false
    end
    
    local success = my_utils.writeToFile(filepath, tostring(value))
    if success then
        log.info("ConfigModule", "Saved " .. key .. ": " .. tostring(value))
    else
        log.error("ConfigModule", "Failed to save " .. key)
    end
    
    return success
end

-- Save a number value to file
function ConfigModule.saveNumber(key, value)
    return ConfigModule.saveString(key, tostring(value))
end

-- Save a boolean value to file
function ConfigModule.saveBoolean(key, value)
    return ConfigModule.saveString(key, tostring(value))
end

-- Save phone number with validation
function ConfigModule.savePhoneNumber(number_key, phone_number)
    -- Validate phone number (should be 8 digits)
    if type(phone_number) ~= "string" or string.len(phone_number) ~= 8 or not string.match(phone_number, "^%d+$") then
        log.error("ConfigModule", "Invalid phone number format: " .. tostring(phone_number))
        return false
    end
    
    local success = ConfigModule.saveString(number_key, phone_number)
    if success then
        -- Update the corresponding variable
        if number_key == "phone1" then
            vars.phone_number1 = phone_number
        elseif number_key == "phone2" then
            vars.phone_number2 = phone_number
        elseif number_key == "phone3" then
            vars.phone_number3 = phone_number
        end
    end
    
    return success
end

-- Save voltage settings
function ConfigModule.saveVoltageSettings(offset, threshold, notify_flag)
    local success = true
    
    if offset ~= nil then
        success = success and ConfigModule.saveNumber("volt_offset", offset)
        vars.voltage_offset = offset
    end
    
    if threshold ~= nil then
        success = success and ConfigModule.saveNumber("volt_threshold", threshold)
        vars.voltage_threshold = threshold
    end
    
    if notify_flag ~= nil then
        success = success and ConfigModule.saveBoolean("volt_notify", notify_flag)
        vars.voltage_notify_flag = notify_flag
    end
    
    return success
end

-- Save system flags
function ConfigModule.saveSystemFlags(alarm, sound, gps, license, geely)
    local success = true
    
    if alarm ~= nil then
        success = success and ConfigModule.saveBoolean("alarm", alarm)
        vars.alarm_state = alarm
    end
    
    if sound ~= nil then
        success = success and ConfigModule.saveBoolean("sound_flag", sound)
        vars.sound_flag = sound
    end
    
    if gps ~= nil then
        success = success and ConfigModule.saveBoolean("gps_flag", gps)
        vars.gps_flag = gps
    end
    
    if license ~= nil then
        success = success and ConfigModule.saveBoolean("license", license)
        vars.isLicensed = license
    end
    
    if geely ~= nil then
        success = success and ConfigModule.saveBoolean("geely_mode", geely)
        vars.geely_atlas_mode = geely
    end
    
    return success
end

-- Save auto-shutdown settings
function ConfigModule.saveAutoShutdown(enabled, minutes)
    local success = true
    
    if enabled ~= nil then
        success = success and ConfigModule.saveBoolean("auto_shutdown", enabled)
        vars.auto_shutdown_enabled = enabled
    end
    
    if minutes ~= nil then
        success = success and ConfigModule.saveNumber("auto_shutdown_time", minutes)
        vars.auto_shutdown_minutes = minutes
        vars.auto_shutdown_time = minutes * 60 * 1000  -- Convert to milliseconds
    end
    
    return success
end

-- Get current configuration as a table
function ConfigModule.getCurrentConfig()
    return {
        phone1 = vars.phone_number1,
        phone2 = vars.phone_number2,
        phone3 = vars.phone_number3,
        volt_offset = vars.voltage_offset,
        volt_threshold = vars.voltage_threshold,
        volt_notify = vars.voltage_notify_flag,
        alarm = vars.alarm_state,
        sound_flag = vars.sound_flag,
        gps_flag = vars.gps_flag,
        license = vars.isLicensed,
        geely_mode = vars.geely_atlas_mode,
        auto_shutdown = vars.auto_shutdown_enabled,
        auto_shutdown_time = vars.auto_shutdown_minutes,
        device_name = vars.device_name,
        mqtt_server = vars.mqtt_server,
        mqtt_port = vars.mqtt_port,
        mqtt_username = vars.mqtt_username,
        mqtt_password = vars.mqtt_password
    }
end

return ConfigModule
