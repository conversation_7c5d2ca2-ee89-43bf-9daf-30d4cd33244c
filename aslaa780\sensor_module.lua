-- sensor_module.lua - Enhanced sensor functionality for Air780EG
-- Combines existing SHTC3, GPS, ADC functionality with AIR720-style interface

local SensorModule = {}

-- Import required modules
local vars = require('variables')
local GPS = require("libgnss_gps")

-- Initialize log module if not available
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO][" .. tag .. "]", ...) end,
        warn = function(tag, ...) print("[WARN][" .. tag .. "]", ...) end,
        error = function(tag, ...) print("[ERROR][" .. tag .. "]", ...) end,
        debug = function(tag, ...) print("[DEBUG][" .. tag .. "]", ...) end
    }
end

-- SHTC3 sensor configuration
local SHTC3 = {
    i2c_id = 0,           -- I2C bus ID
    addr = 0x70,          -- SHTC3 I2C address (0x70)
    cmd_wakeup = 0x3517,  -- Wake up command
    cmd_sleep = 0xB098,   -- Sleep command
    cmd_soft_reset = 0x805D,  -- Soft reset command
    cmd_read_id = 0xEFC8,     -- Read ID command
    cmd_measure_normal = 0x7866,  -- Normal mode measurement
    temp_offset = 0       -- Temperature calibration offset
}

-- ADC configuration
local ADC_CONFIG = {
    id = 1,                -- ADC ID (ADC1 on pin 96 on Air780EG)
    channel = 0,           -- Using channel 0 for ADC1
    reference_voltage = 3.3, -- Reference voltage in volts
    scaling_factor = 16.14 -- Voltage conversion factor
}

-- Initialize sensor module
function SensorModule.init()
    log.info("SensorModule", "Initializing sensor module...")
    
    -- Initialize SHTC3 sensor
    local shtc3_success = SensorModule.initSHTC3()
    if not shtc3_success then
        log.warn("SensorModule", "Failed to initialize SHTC3 sensor")
    end
    
    -- Initialize ADC
    local adc_success = SensorModule.initADC()
    if not adc_success then
        log.warn("SensorModule", "Failed to initialize ADC")
    end
    
    -- Initialize GPS
    local gps_success = SensorModule.initGPS()
    if not gps_success then
        log.warn("SensorModule", "Failed to initialize GPS")
    end
    
    log.info("SensorModule", "Sensor module initialization complete")
    return true
end

-- Initialize SHTC3 sensor
function SensorModule.initSHTC3()
    log.info("SensorModule", "Initializing SHTC3 sensor...")
    
    -- Initialize I2C with 100kHz speed
    local setup_result = i2c.setup(SHTC3.i2c_id, 100000)
    if setup_result ~= 1 then
        log.error("SensorModule", "Failed to initialize I2C bus")
        return false
    end
    
    -- Try to communicate with SHTC3
    local result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {0x00})
    if not result then
        log.error("SensorModule", "SHTC3 sensor not found")
        return false
    end
    
    -- Soft reset the sensor
    local reset_cmd_high = (SHTC3.cmd_soft_reset >> 8) & 0xFF
    local reset_cmd_low = SHTC3.cmd_soft_reset & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {reset_cmd_high, reset_cmd_low})
    sys.wait(1)
    
    log.info("SensorModule", "SHTC3 sensor initialized successfully")
    return true
end

-- Initialize ADC
function SensorModule.initADC()
    log.info("SensorModule", "Initializing ADC...")
    
    local result = adc.open(ADC_CONFIG.id, ADC_CONFIG.channel)
    if result == true or result == 1 then
        log.info("SensorModule", "ADC initialized successfully")
        return true
    else
        log.error("SensorModule", "Failed to initialize ADC")
        return false
    end
end

-- Initialize GPS
function SensorModule.initGPS()
    log.info("SensorModule", "Initializing GPS...")
    
    if not GPS then
        log.error("SensorModule", "GPS module not available")
        return false
    end
    
    local success = GPS.init()
    if success then
        log.info("SensorModule", "GPS initialized successfully")
        return true
    else
        log.error("SensorModule", "Failed to initialize GPS")
        return false
    end
end

-- Read humidity and temperature from SHTC3 (AIR720-style interface)
function SensorModule.readHumidityTemperature()
    -- Wake up the sensor
    local wakeup_cmd_high = (SHTC3.cmd_wakeup >> 8) & 0xFF
    local wakeup_cmd_low = SHTC3.cmd_wakeup & 0xFF
    local wakeup_result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {wakeup_cmd_high, wakeup_cmd_low})
    if not wakeup_result then
        log.error("SensorModule", "Failed to wake up SHTC3 sensor")
        return 50.0, 25.0  -- Return default values
    end
    sys.wait(1)
    
    -- Trigger measurement
    local measure_cmd_high = (SHTC3.cmd_measure_normal >> 8) & 0xFF
    local measure_cmd_low = SHTC3.cmd_measure_normal & 0xFF
    local send_result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {measure_cmd_high, measure_cmd_low})
    if not send_result then
        log.error("SensorModule", "Failed to send SHTC3 measurement command")
        return 50.0, 25.0  -- Return default values
    end
    
    -- Wait for measurement
    sys.wait(15)
    
    -- Read data
    local data = i2c.recv(SHTC3.i2c_id, SHTC3.addr, 6)
    if not data or #data ~= 6 then
        log.error("SensorModule", "Failed to read SHTC3 data")
        return 50.0, 25.0  -- Return default values
    end
    
    -- Extract temperature
    local temp_raw = (data:byte(1) << 8) | data:byte(2)
    local temperature = -45.0 + 175.0 * (temp_raw / 65535.0)
    
    -- Extract humidity
    local humidity_raw = (data:byte(4) << 8) | data:byte(5)
    local humidity = 100.0 * (humidity_raw / 65535.0)
    
    -- Apply calibration offset
    temperature = temperature + (vars.shtc3_temp_offset or 0)
    
    -- Put sensor back to sleep
    local sleep_cmd_high = (SHTC3.cmd_sleep >> 8) & 0xFF
    local sleep_cmd_low = SHTC3.cmd_sleep & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {sleep_cmd_high, sleep_cmd_low})
    
    return humidity, temperature
end

-- Read voltage from ADC (AIR720-style interface)
function SensorModule.readVoltage()
    local adcval, voltage_mv = adc.read(ADC_CONFIG.id, ADC_CONFIG.channel)
    
    if not voltage_mv then
        log.error("SensorModule", "Failed to read ADC voltage")
        return 12.0  -- Return default value
    end
    
    -- Convert millivolts to volts using the voltage factor formula
    local voltage = (voltage_mv * ADC_CONFIG.scaling_factor) / 1000.0
    
    -- Apply voltage offset if available
    if vars.voltage_offset then
        voltage = voltage + vars.voltage_offset
    end
    
    return voltage
end

-- Read GPS data (AIR720-style interface)
function SensorModule.readGPS()
    if not GPS then
        return "0 N", "0 E", 0  -- Return default values
    end
    
    local position = GPS.getPosition()
    if not position or not position.latitude or not position.longitude then
        return "0 N", "0 E", 0  -- Return default values
    end
    
    -- Convert from DDMM.MMMMM to DD.DDDDD format and format as strings
    local lat_deg = math.floor(position.latitude / 100)
    local lat_min = position.latitude - (lat_deg * 100)
    local lat_decimal = lat_deg + (lat_min / 60)
    local lat_str = string.format("%.5f N", lat_decimal)
    
    local lon_deg = math.floor(position.longitude / 100)
    local lon_min = position.longitude - (lon_deg * 100)
    local lon_decimal = lon_deg + (lon_min / 60)
    local lon_str = string.format("%.5f E", lon_decimal)
    
    local speed = position.speed or 0
    
    return lat_str, lon_str, speed
end

-- Check if GPS is available
function SensorModule.isGPSAvailable()
    return GPS ~= nil
end

-- Get mobile signal strength (RSSI)
function SensorModule.getRSSI()
    -- Try to get signal strength using mobile.signal()
    if mobile and mobile.signal then
        local rssi = mobile.signal()
        if rssi then
            return rssi
        end
    end
    
    -- Alternative method for Air780EG - return direct CSQ value
    if mobile and mobile.csq then
        local csq = mobile.csq()
        if csq then
            return csq
        end
    end
    
    return 0  -- Default value
end

-- Get all sensor readings at once
function SensorModule.readAllSensors()
    local humidity, temperature = SensorModule.readHumidityTemperature()
    local voltage = SensorModule.readVoltage()
    local lat, lon, speed = SensorModule.readGPS()
    local rssi = SensorModule.getRSSI()
    
    return {
        humidity = humidity,
        temperature = temperature,
        voltage = voltage,
        latitude = lat,
        longitude = lon,
        speed = speed,
        rssi = rssi
    }
end

return SensorModule
