-- my_utils.lua - Enhanced utility functions for Air780EG
-- Adapted from AIR720BLE my_utils.lua with Air780-specific enhancements

local my_utils = {}

-- Import required modules
local vars = require('variables')
local PinModule = require('pin_module')

-- Initialize log module if not available
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO][" .. tag .. "]", ...) end,
        warn = function(tag, ...) print("[WARN][" .. tag .. "]", ...) end,
        error = function(tag, ...) print("[ERROR][" .. tag .. "]", ...) end,
        debug = function(tag, ...) print("[DEBUG][" .. tag .. "]", ...) end
    }
end

-- Check if a file exists
function my_utils.fileExists(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end
    return false
end

-- Read content from a file
function my_utils.readFile(path)
    local file, err = io.open(path, "r")
    if not file then
        log.error("readFile", "Failed to open file for reading: " .. tostring(err))
        return nil
    end

    local content = file:read("*a")
    file:close()
    return content
end

-- Write content to a file
function my_utils.writeToFile(path, content)
    local file, err = io.open(path, "w")
    if not file then
        log.error("writeToFile", "Failed to open file for writing: " .. tostring(err))
        return false
    end

    -- Convert data to string if it's not already a string
    if type(content) == "boolean" then
        content = tostring(content)
    elseif type(content) ~= "string" and type(content) ~= "number" then
        log.warn("writeToFile", "Converting non-string/non-number data to string: " .. type(content))
        content = tostring(content)
    end

    file:write(content)
    file:close()
    return true
end

-- Create a directory with fallback options
function my_utils.createDirectory(dirPath)
    -- Check if directory already exists
    if my_utils.fileExists(dirPath) then
        return true
    end

    -- Try to create directory using lfs if available
    if lfs and lfs.mkdir then
        local success, err = pcall(lfs.mkdir, dirPath)
        if success then
            log.info("createDirectory", "Directory created successfully: " .. dirPath)
            return true
        else
            log.warn("createDirectory", "Failed to create directory with lfs: " .. tostring(err))
        end
    end

    -- Fallback: try to create a test file in the directory
    local testFile = dirPath .. "/test.tmp"
    local success = my_utils.writeToFile(testFile, "test")
    if success then
        my_utils.deleteFile(testFile)
        log.info("createDirectory", "Directory appears to be accessible: " .. dirPath)
        return true
    end

    log.error("createDirectory", "Failed to create or access directory: " .. dirPath)
    return false
end

-- Append content to a file
function my_utils.appendToFile(path, content)
    local file = io.open(path, "a+")
    if not file then
        return false
    end

    file:write(content)
    file:close()
    return true
end

-- Delete a file
function my_utils.deleteFile(path)
    if not my_utils.fileExists(path) then
        return true -- File doesn't exist, so consider it deleted
    end

    local success, err = os.remove(path)
    return success, err
end

-- Format a number to a specific number of decimal places
function my_utils.formatNumber(num, decimals)
    decimals = decimals or 2
    local mult = 10^decimals
    return math.floor(num * mult + 0.5) / mult
end

-- Get current timestamp in ISO format
function my_utils.getISOTimestamp()
    local time = os.date("*t")
    return string.format("%04d-%02d-%02dT%02d:%02d:%02d",
        time.year, time.month, time.day, time.hour, time.min, time.sec)
end

-- Function to pack sensor and state data into JSON format
-- This function reads current sensor data and formats it for MQTT transmission
function my_utils.packJsonData()
    local jsonData = {}

    -- Read sensor data (using existing Air780 functions)
    local temp_hum = nil
    local temperature = 25.0  -- Default values
    local humidity = 50.0
    local voltage = 12.0
    local gps = nil
    local rssi = 0

    -- Try to read SHTC3 sensor data
    if readSHTC3 then
        temp_hum = readSHTC3()
        if temp_hum then
            temperature = temp_hum.temperature or 25.0
            humidity = temp_hum.humidity or 50.0
        end
    end

    -- Try to read voltage
    if readVoltage then
        voltage = readVoltage() or 12.0
    end

    -- Try to read GPS
    if getGPSPosition then
        gps = getGPSPosition()
    end

    -- Try to read RSSI
    if getRSSI then
        rssi = getRSSI() or 0
    end

    -- Format GPS coordinates
    local lat_str = "0 N"
    local lon_str = "0 E"
    local speed = 0

    if gps and gps.latitude and gps.longitude then
        -- Convert from DDMM.MMMMM to DD.DDDDD format
        local lat_deg = math.floor(gps.latitude / 100)
        local lat_min = gps.latitude - (lat_deg * 100)
        local lat_decimal = lat_deg + (lat_min / 60)
        lat_str = string.format("%.5f N", lat_decimal)

        local lon_deg = math.floor(gps.longitude / 100)
        local lon_min = gps.longitude - (lon_deg * 100)
        local lon_decimal = lon_deg + (lon_min / 60)
        lon_str = string.format("%.5f E", lon_decimal)

        speed = gps.speed or 0
    end

    -- Check if gps_flag is false, and set GPS values to 0 if so
    if vars.gps_flag == false then
        lat_str = "0 N"
        lon_str = "0 E"
        speed = 0
    end

    -- Build JSON data structure
    jsonData["temp"] = math.floor(temperature + 0.5)  -- Round to nearest integer
    jsonData["hum"] = math.floor(humidity + 0.5)      -- Round to nearest integer
    jsonData["Lat"] = lat_str
    jsonData["Lon"] = lon_str
    jsonData["Speed"] = speed
    jsonData["volt"] = my_utils.formatNumber(voltage, 3)  -- 3 decimal places

    -- Read pin states for motion and light sensors
    jsonData["motion"] = PinModule.readPinState("S1") == 1 and 0 or 1
    jsonData["light"] = PinModule.readPinState("S2") == 1 and 0 or 1

    -- Add version and signal strength
    jsonData["ver"] = VERSION or "1.0.0"
    jsonData["rssi"] = rssi

    -- Encode as JSON
    local encodedJson = json.encode(jsonData)
    log.info("PackJsonData", encodedJson)
    return encodedJson
end

-- Function to pack formatted data for SMS messages
function my_utils.packFormattedData()
    -- Reuse the packJsonData method to get sensor data
    local jsonData = my_utils.packJsonData()
    local success, decodedJson = pcall(json.decode, jsonData)

    -- Handle decoding errors
    if not success then
        log.error("PackFormattedData", "Failed to decode JSON data")
        return "Error packing data"
    end

    -- Extract relevant data from decoded JSON
    local latitude = decodedJson["Lat"]
    local longitude = decodedJson["Lon"]
    local voltage = tonumber(decodedJson["volt"]) or 0
    local temperature = tonumber(decodedJson["temp"]) or 0
    local humidity = tonumber(decodedJson["hum"]) or 0

    -- Check if GPS data is available
    local gpsMessage
    if not latitude or not longitude or (latitude == "0 N" and longitude == "0 E") then
        gpsMessage = "GPS signal lost"
    else
        -- Remove spaces for URL formatting
        latitude = latitude:gsub(" ", "")
        longitude = longitude:gsub(" ", "")
        gpsMessage = string.format("https://maps.google.com/?q=%s,%s", latitude, longitude)
    end

    -- Format the message
    local formattedMessage = string.format(
        "Tanii mashin:\n%s\nBatt: %.2fV | Temp: %.1fC\nHum: %.1f%%",
        gpsMessage, voltage, temperature, humidity
    )

    log.info("PackFormattedData", formattedMessage)
    return formattedMessage
end

-- Function to safely send MQTT messages with retries
function my_utils.safeMqttSend(topic, message, qos, max_retries, retry_delay)
    -- Set default values if not provided
    max_retries = max_retries or 3
    retry_delay = retry_delay or 1000  -- 1 second
    qos = qos or 0

    -- Check if mqtt_module is available
    local mqtt_module = require('mqtt_module')
    if not mqtt_module then
        log.error("MQTT", "mqtt_module not available")
        return false, "mqtt_module not available"
    end

    local attempts = 0
    while attempts < max_retries do
        attempts = attempts + 1

        -- Check if MQTT is ready
        if mqtt_module.is_ready() then
            local success = mqtt_module.publish(message, topic)
            if success then
                log.info("MQTT", "Message sent successfully on attempt " .. attempts)
                return true, "Success"
            else
                log.warn("MQTT", "Failed to send message on attempt " .. attempts)
            end
        else
            log.warn("MQTT", "MQTT not ready on attempt " .. attempts)
        end

        -- Wait before retrying (except on last attempt)
        if attempts < max_retries then
            if sys and sys.wait then
                sys.wait(retry_delay)
            end
        end
    end

    log.error("MQTT", "Failed to send message after " .. max_retries .. " attempts")
    return false, "Max retries exceeded"
end

-- Function to check MQTT connection status and diagnostics
function my_utils.checkMqttStatus()
    local mqtt_module = require('mqtt_module')

    -- Check MQTT connection status
    local mqtt_connected = mqtt_module and mqtt_module.is_ready() or false
    local mqtt_error = "None"

    -- Check network status
    local net_status = "Unknown"
    if mobile and mobile.status then
        local status = mobile.status()
        if status == "REGISTERED" or status == "REGISTERED_ROAMING" or
           status == 1 or status == 2 or status == 5 then
            net_status = "Connected"
        else
            net_status = "Disconnected"
        end
    end

    -- Get signal strength
    local signal = "Unknown"
    if getRSSI then
        local rssi = getRSSI()
        if rssi then
            signal = rssi
        end
    end

    -- Return diagnostic info
    return {
        mqtt_connected = mqtt_connected,
        mqtt_error = mqtt_error,
        network_status = net_status,
        signal_strength = signal,
        client_id = mqtt_module and mqtt_module.get_client_id() or "Unknown",
        uptime = os.time(),
        memory_free = collectgarbage("count")
    }
end

-- Function to load configuration from files
function my_utils.loadConfiguration()
    log.info("Config", "Loading configuration from files...")

    -- Create the /user_dir directory
    local dirCreated = my_utils.createDirectory("/user_dir")
    if not dirCreated then
        log.warn("Config", "Could not create /user_dir directory, but will attempt to use it anyway")
    end

    -- Load phone numbers
    if my_utils.fileExists("/user_dir/phone1.txt") then
        vars.phone_number1 = my_utils.readFile("/user_dir/phone1.txt")
        log.info("Config", "Loaded phone1: " .. (vars.phone_number1 or "nil"))
    end
    if my_utils.fileExists("/user_dir/phone2.txt") then
        vars.phone_number2 = my_utils.readFile("/user_dir/phone2.txt")
        log.info("Config", "Loaded phone2: " .. (vars.phone_number2 or "nil"))
    end
    if my_utils.fileExists("/user_dir/phone3.txt") then
        vars.phone_number3 = my_utils.readFile("/user_dir/phone3.txt")
        log.info("Config", "Loaded phone3: " .. (vars.phone_number3 or "nil"))
    end

    -- Load voltage settings
    if my_utils.fileExists("/user_dir/volt_offset.txt") then
        local content = my_utils.readFile("/user_dir/volt_offset.txt")
        vars.voltage_offset = tonumber(content) or 0
        log.info("Config", "Loaded voltage offset: " .. vars.voltage_offset)
    end

    if my_utils.fileExists("/user_dir/volt_threshold.txt") then
        local content = my_utils.readFile("/user_dir/volt_threshold.txt")
        vars.voltage_threshold = tonumber(content) or 0.5
        log.info("Config", "Loaded voltage threshold: " .. vars.voltage_threshold)
    end

    if my_utils.fileExists("/user_dir/volt_notify.txt") then
        local content = my_utils.readFile("/user_dir/volt_notify.txt")
        vars.voltage_notify_flag = (content == "true")
        log.info("Config", "Loaded voltage notify flag: " .. tostring(vars.voltage_notify_flag))
    else
        vars.voltage_notify_flag = false
        my_utils.writeToFile("/user_dir/volt_notify.txt", tostring(vars.voltage_notify_flag))
    end

    -- Load other settings
    if my_utils.fileExists("/user_dir/alarm.txt") then
        local content = my_utils.readFile("/user_dir/alarm.txt")
        vars.alarm_state = (content == "true")
        log.info("Config", "Loaded alarm state: " .. tostring(vars.alarm_state))
    else
        vars.alarm_state = false
        my_utils.writeToFile("/user_dir/alarm.txt", tostring(vars.alarm_state))
    end

    if my_utils.fileExists("/user_dir/sound_flag.txt") then
        local content = my_utils.readFile("/user_dir/sound_flag.txt")
        vars.sound_flag = (content == "true")
        log.info("Config", "Loaded sound flag: " .. tostring(vars.sound_flag))
    else
        vars.sound_flag = true
        my_utils.writeToFile("/user_dir/sound_flag.txt", tostring(vars.sound_flag))
    end

    if my_utils.fileExists("/user_dir/license.txt") then
        local content = my_utils.readFile("/user_dir/license.txt")
        vars.isLicensed = (content == "true")
        log.info("Config", "Loaded license status: " .. tostring(vars.isLicensed))
    else
        vars.isLicensed = true
        my_utils.writeToFile("/user_dir/license.txt", tostring(vars.isLicensed))
    end

    log.info("Config", "Configuration loading complete")
end

return my_utils
