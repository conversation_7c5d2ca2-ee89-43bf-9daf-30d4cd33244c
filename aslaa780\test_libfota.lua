-- test_libfota.lua - Simple test to check libfota availability

print("=== Testing libfota availability ===")

-- Test 1: Try to require libfota
local success, libfota = pcall(require, "libfota")
print("libfota require result:", success)

if success and libfota then
    print("libfota module loaded successfully!")
    
    -- Test 2: Check available functions
    print("Available libfota functions:")
    for k, v in pairs(libfota) do
        if type(v) == "function" then
            print("- libfota." .. k)
        end
    end
    
    -- Test 3: Check if request function exists
    if libfota.request then
        print("✓ libfota.request is available")
    else
        print("✗ libfota.request is NOT available")
    end
    
else
    print("✗ libfota module is NOT available")
    print("Error:", libfota)
end

-- Test 4: Check global variables
print("\nGlobal variables check:")
print("- PROJECT:", _G.PROJECT or "not set")
print("- VERSION:", _G.VERSION or "not set") 
print("- PRODUCT_KEY:", _G.PRODUCT_KEY or "not set")

-- Test 5: Check mobile module
if mobile and mobile.imei then
    local imei = mobile.imei()
    print("- IMEI:", imei or "not available")
else
    print("- IMEI: mobile module not available")
end

print("=== Test complete ===")
