-- commands.lua - Command implementations for Air780EG
-- Adapted from AIR720BLE commands.lua for Air780 compatibility

local commands = {}

-- Import required modules
local vars = require('variables')
local PinModule = require('pin_module')
local my_utils = require('my_utils')

-- Initialize log module if not available
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO][" .. tag .. "]", ...) end,
        warn = function(tag, ...) print("[WARN][" .. tag .. "]", ...) end,
        error = function(tag, ...) print("[ERROR][" .. tag .. "]", ...) end,
        debug = function(tag, ...) print("[DEBUG][" .. tag .. "]", ...) end
    }
end

-- Function to play beep instead of MP3 (Air780 doesn't have audio files)
local function playBeep(beepType)
    if vars.sound_flag then
        sys.taskInit(function()
            if beepType == "check" then
                PinModule.beepPattern(1, 300, 0, 2000)  -- Single beep for check
            elseif beepType == "lock" then
                PinModule.beepPattern(2, 200, 200, 2500)  -- Two beeps for lock
            elseif beepType == "unlock" then
                PinModule.beepPattern(3, 150, 150, 3000)  -- Three beeps for unlock
            elseif beepType == "as" then
                PinModule.beepPattern(1, 500, 0, 1800)  -- Long beep for remote start
            elseif beepType == "success" then
                PinModule.beepPattern(1, 400, 0, 2500)  -- Success beep
            elseif beepType == "untar" then
                PinModule.beepPattern(2, 300, 300, 1500)  -- Two lower beeps for untar
            else
                PinModule.beepPattern(1, 200, 0, 2000)  -- Default beep
            end
        end)
    end
end

-- Check command - publish sensor data
function commands.checkCommand()
    log.info("commands", "Executing check command")
    playBeep("check")
    
    -- Publish sensor data via MQTT
    local mqtt_module = require('mqtt_module')
    if mqtt_module and mqtt_module.is_ready() then
        local json_data = my_utils.packJsonData()
        local success = mqtt_module.publish(json_data)
        if success then
            log.info("commands", "Check command: sensor data published")
        else
            log.error("commands", "Check command: failed to publish sensor data")
        end
    else
        log.warn("commands", "Check command: MQTT not ready")
    end
end

-- Lock command
function commands.lockCommand()
    log.info("commands", "Executing lock command")
    
    -- Update relay state
    vars.relay1_state = 1
    
    sys.taskInit(function()
        log.info("commands", "Lock sequence started")
        playBeep("lock")
        
        -- Wait before pressing lock button
        sys.wait(vars.lock_wait_duration)
        
        -- Press lock button (Key1)
        PinModule.relayControl("Key1", 1)
        sys.wait(vars.lock_press_duration)
        PinModule.relayControl("Key1", 0)
        
        -- Turn off key power if key_state is false
        if vars.key_state == false then
            PinModule.relayControl("KeyPower", 0)
        end
        
        log.info("commands", "Lock sequence completed")
    end)
end

-- Unlock command
function commands.unlockCommand()
    log.info("commands", "Executing unlock command")
    
    -- Update relay state
    vars.relay2_state = 1
    
    sys.taskInit(function()
        log.info("commands", "Unlock sequence started")
        playBeep("unlock")
        
        -- Wait before pressing unlock button
        sys.wait(vars.unlock_wait_duration)
        
        -- Press unlock button (Key2)
        PinModule.relayControl("Key2", 1)
        sys.wait(vars.unlock_press_duration)
        PinModule.relayControl("Key2", 0)
        
        -- Turn off key power if key_state is false
        if vars.key_state == false then
            PinModule.relayControl("KeyPower", 0)
        end
        
        log.info("commands", "Unlock sequence completed")
    end)
end

-- Mirror command - alternates between lock and unlock for Geely Atlas mode
local last_mirror_action = "none"
local GEELY_UNLOCK_DURATION = 8000  -- 8 seconds for unlock in Geely mode
local GEELY_LOCK_DURATION = 2000    -- 2 seconds for lock in Geely mode

function commands.mirrorCommand()
    log.info("commands", "Executing mirror command")
    
    -- Initialize key power
    PinModule.relayControl("KeyPower", 1)
    
    sys.taskInit(function()
        -- Check if we're in Geely Atlas mode
        if vars.geely_atlas_mode then
            log.info("VehicleMode", "Using Geely Atlas sequence for mirror command")
            
            -- Alternate between unlock and lock commands
            if last_mirror_action == "unlock" or last_mirror_action == "none" then
                -- Execute lock command
                log.info("GeelyAtlas", "Mirror executing lock command")
                PinModule.relayControl("Key1", 1)  -- Lock button press
                sys.wait(vars.mirror_duration or GEELY_LOCK_DURATION)
                PinModule.relayControl("Key1", 0)  -- Release lock button
                last_mirror_action = "lock"
            else
                -- Execute unlock command
                log.info("GeelyAtlas", "Mirror executing unlock command")
                PinModule.relayControl("Key2", 1)  -- Unlock button press
                sys.wait(GEELY_UNLOCK_DURATION)  -- Fixed 8 seconds for unlock
                PinModule.relayControl("Key2", 0)  -- Release unlock button
                last_mirror_action = "unlock"
            end
        else
            -- Standard mirror operation
            log.info("commands", "Standard mirror operation")
            sys.wait(vars.mirror_duration)
        end
        
        -- Turn off key power if key_state is false
        if vars.key_state == false then
            PinModule.relayControl("KeyPower", 0)
        end
        
        log.info("commands", "Mirror command completed")
    end)
end

-- Remote start (as) command
function commands.asCommand()
    log.info("commands", "Executing remote start (as) command")
    
    -- Check if the device is licensed
    if not vars.isLicensed then
        log.warn("commands", "Device not licensed, cannot execute as command")
        return
    end
    
    -- Initialize key power
    PinModule.relayControl("KeyPower", 1)
    
    -- Update states
    vars.relay1_state = 1
    vars.asa_state = true
    vars.carAlreadyStarted = true
    vars.last_as_command_time = os.time()
    
    sys.taskInit(function()
        -- Check if we're in Geely Atlas mode
        if vars.geely_atlas_mode then
            log.info("VehicleMode", "Using Geely Atlas sequence for asCommand")
            sys.wait(vars.lock_init_duration)  -- Wait for key init
            
            playBeep("as")
            
            -- First lock sequence
            log.info("GeelyAtlas", "First lock press")
            PinModule.relayControl("Key1", 1)
            sys.wait(vars.lock_press_duration)
            PinModule.relayControl("Key1", 0)
            
            -- Wait between presses
            sys.wait(vars.between_press_duration)
            
            -- Second lock sequence (remote start)
            log.info("GeelyAtlas", "Second lock press (remote start)")
            PinModule.relayControl("Key1", 1)
            sys.wait(vars.remote_start_duration)
            PinModule.relayControl("Key1", 0)
        else
            -- Standard remote start operation
            log.info("commands", "Standard remote start operation")
            sys.wait(vars.lock_init_duration)
            
            playBeep("as")
            
            -- Turn on Relay1 for remote start
            PinModule.relayControl("Relay1", 1)
            sys.wait(vars.relay1_on_duration)
            PinModule.relayControl("Relay1", 0)
        end
        
        -- Set up auto-shutdown timer if enabled
        if vars.auto_shutdown_enabled then
            if vars.auto_shutdown_timer_id then
                sys.timerStop(vars.auto_shutdown_timer_id)
            end
            
            vars.auto_shutdown_timer_id = sys.timerLoopStart(function()
                log.info("AutoShutdown", "Auto-shutdown timer triggered")
                commands.untarCommand()
            end, vars.auto_shutdown_time)
            
            log.info("AutoShutdown", string.format("Auto-shutdown timer set for %d minutes", vars.auto_shutdown_minutes))
        end
        
        log.info("commands", "Remote start command completed")
    end)
end

-- Turn off (untar) command
function commands.untarCommand(delay_ms)
    log.info("commands", "Executing turn off (untar) command")
    
    delay_ms = delay_ms or 2000  -- Default 2 second delay
    
    -- Cancel any existing auto-shutdown timer
    if vars.auto_shutdown_timer_id then
        log.info("AutoShutdown", "Canceling auto-shutdown timer due to manual untar command")
        sys.timerStop(vars.auto_shutdown_timer_id)
        vars.auto_shutdown_timer_id = nil
    end
    
    -- Initialize key power
    PinModule.relayControl("KeyPower", 1)
    
    -- Update states
    vars.relay1_state = 0
    vars.asa_state = false
    vars.carAlreadyStarted = false
    
    sys.taskInit(function()
        playBeep("untar")
        
        -- Wait for specified delay
        sys.wait(delay_ms)
        
        -- Turn off all relays
        PinModule.relayControl("Relay1", 0)
        PinModule.relayControl("Relay2", 0)
        PinModule.relayControl("Relay3", 0)
        
        -- Turn off key power if key_state is false
        if vars.key_state == false then
            PinModule.relayControl("KeyPower", 0)
        end
        
        log.info("commands", "Turn off command completed")
    end)
end

-- Test command - turns on all outputs briefly
function commands.testCommand()
    log.info("commands", "Executing test command")
    
    -- Initialize key power
    PinModule.relayControl("KeyPower", 1)
    
    sys.taskInit(function()
        sys.wait(1000)  -- Wait 1 second
        playBeep("as")
        
        -- Turn on all relays
        vars.relay1_state = 1
        PinModule.relayControl("Relay1", 1)
        PinModule.relayControl("Relay2", 1)
        PinModule.relayControl("Relay3", 1)
        
        -- Wait for test duration
        sys.wait(3000)  -- 3 seconds
        
        -- Turn off all relays
        vars.relay1_state = 0
        PinModule.relayControl("Relay1", 0)
        PinModule.relayControl("Relay2", 0)
        PinModule.relayControl("Relay3", 0)
        
        -- Turn off key power if key_state is false
        if vars.key_state == false then
            PinModule.relayControl("KeyPower", 0)
        end
        
        log.info("commands", "Test command completed")
        
        -- Publish sensor data
        local mqtt_module = require('mqtt_module')
        if mqtt_module and mqtt_module.is_ready() then
            local json_data = my_utils.packJsonData()
            mqtt_module.publish(json_data)
        end
    end)
end

-- Function to get all timing parameters
function commands.getTimingParameters()
    local params = {
        lock_press = vars.lock_press_duration,
        unlock_press = vars.unlock_press_duration,
        lock_wait = vars.lock_wait_duration,
        unlock_wait = vars.unlock_wait_duration,
        between_press = vars.between_press_duration,
        remote_start = vars.remote_start_duration,
        mirror = vars.mirror_duration,
        relay1_on = vars.relay1_on_duration,
        relay2_on = vars.relay2_on_duration,
        lock_init = vars.lock_init_duration
    }
    
    return params
end

return commands
