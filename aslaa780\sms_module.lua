-- sms_module.lua - SMS functionality for Air780EG
-- Adapted from AIR720BLE SmsModule.lua for Air780 compatibility

local SmsModule = {}

-- Import required modules
local vars = require('variables')
local my_utils = require('my_utils')
local PinModule = require('pin_module')

-- Initialize log module if not available
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO][" .. tag .. "]", ...) end,
        warn = function(tag, ...) print("[WARN][" .. tag .. "]", ...) end,
        error = function(tag, ...) print("[ERROR][" .. tag .. "]", ...) end,
        debug = function(tag, ...) print("[DEBUG][" .. tag .. "]", ...) end
    }
end

-- Initialize SMS module
function SmsModule.init()
    -- Check if SMS module is available
    if not sms then
        log.warn("SmsModule", "SMS module not available on this platform")
        return false
    end
    
    -- Set up SMS callback for incoming messages
    sms.setNewSmsCb(SmsModule.smsCallback)
    log.info("SmsModule", "SMS Module Initialized")
    return true
end

-- Function to send an SMS
function SmsModule.sendSms(phoneNumber, message)
    if not sms then
        log.error("SmsModule", "SMS module not available")
        return false
    end
    
    log.info("SmsModule", "Sending SMS to: " .. phoneNumber .. " with message: " .. message)
    local result = sms.send(phoneNumber, message)
    if result then
        log.info("SmsModule", "SMS sent successfully")
        return true
    else
        log.error("SmsModule", "Failed to send SMS")
        return false
    end
end

-- Function to send SMS to multiple phone numbers safely
function SmsModule.sendLicenseNotify()
    local message = "Tani license duussan bna. SMS uilchilgee haagdaj bna"
    log.warn("SMS", "License notify via sms.")

    -- Send SMS to phone_number1 if it's valid (not nil and not empty)
    if vars.phone_number1 and vars.phone_number1 ~= "" then
        local success = SmsModule.sendSms(vars.phone_number1, message)
        if not success then
            log.error("sendLicenseNotify", "Failed to send SMS to " .. vars.phone_number1)
        end
    end

    -- Send SMS to phone_number2 if it's valid
    if vars.phone_number2 and vars.phone_number2 ~= "" then
        local success = SmsModule.sendSms(vars.phone_number2, message)
        if not success then
            log.error("sendLicenseNotify", "Failed to send SMS to " .. vars.phone_number2)
        end
    end

    -- Send SMS to phone_number3 if it's valid
    if vars.phone_number3 and vars.phone_number3 ~= "" then
        local success = SmsModule.sendSms(vars.phone_number3, message)
        if not success then
            log.error("sendLicenseNotify", "Failed to send SMS to " .. vars.phone_number3)
        end
    end
end

-- Callback function to handle incoming SMS
function SmsModule.smsCallback(num, data, datetime)
    num = string.sub(num, -8) -- Get the last 8 digits of the phone number
    log.info("SmsModule", "Received SMS from: " .. num .. " with content: " .. data .. " at " .. datetime)

    -- Keywords to detect for forwarding to MQTT
    local keywords = {"Tand", "TG", "hugatsaa", "kod", "code", "opt"}

    -- Check if the SMS contains any of the keywords
    for _, keyword in ipairs(keywords) do
        if string.find(data, keyword) then
            -- Use the actual SMS content as the message
            local message = string.format('{"sms":"%s"}', data:gsub('"', '\\"'):gsub('\\', '\\\\'))

            -- Send to MQTT server using mqtt_module
            local mqtt_module = require('mqtt_module')
            if mqtt_module and mqtt_module.is_ready() then
                local success = mqtt_module.publish(message)
                if success then
                    log.info("SmsModule", "Message sent to MQTT server: " .. message)
                else
                    log.error("SmsModule", "Failed to send message to MQTT server")
                end
            else
                log.warn("SmsModule", "MQTT not ready, cannot forward SMS")
            end
            return
        end
    end

    -- Handle restart command
    if string.lower(data) == "restart" then
        log.info("SmsModule", "Received restart command, restarting the system...")
        SmsModule.sendSms(num, "System restarting...")
        
        -- Restart the system
        sys.taskInit(function()
            sys.wait(2000)  -- Wait 2 seconds before restart
            if rtos and rtos.reboot then
                rtos.reboot()
            else
                log.error("SmsModule", "Reboot function not available")
            end
        end)
        return
    end

    -- Handle phone number configuration
    if string.find(data, "x123456x1") then
        vars.phone_number1 = string.sub(data, string.find(data, "x123456x1") + string.len("x123456x1"), string.find(data, "xx") - 1)
        vars.phone_number1 = string.sub(vars.phone_number1, -8)
        log.info("Phone number change", vars.phone_number1)
        if string.len(vars.phone_number1) == 8 and my_utils.writeToFile("/user_dir/phone1.txt", vars.phone_number1) then
            SmsModule.sendSms(num, "success")
        else
            SmsModule.sendSms(num, "fail")
        end
        return
    end

    if string.find(data, "x123456x2") then
        vars.phone_number2 = string.sub(data, string.find(data, "x123456x2") + string.len("x123456x2"), string.find(data, "xx") - 1)
        vars.phone_number2 = string.sub(vars.phone_number2, -8)
        log.info("Phone number change", vars.phone_number2)
        if string.len(vars.phone_number2) == 8 and my_utils.writeToFile("/user_dir/phone2.txt", vars.phone_number2) then
            SmsModule.sendSms(num, "success")
        else
            SmsModule.sendSms(num, "fail")
        end
        return
    end

    if string.find(data, "x123456x3") then
        vars.phone_number3 = string.sub(data, string.find(data, "x123456x3") + string.len("x123456x3"), string.find(data, "xx") - 1)
        vars.phone_number3 = string.sub(vars.phone_number3, -8)
        log.info("Phone number change", vars.phone_number3)
        if string.len(vars.phone_number3) == 8 and my_utils.writeToFile("/user_dir/phone3.txt", vars.phone_number3) then
            SmsModule.sendSms(num, "success")
        else
            SmsModule.sendSms(num, "fail")
        end
        return
    end

    -- Handle unitel command format "unitel:88889999 1000"
    if string.match(data, "^unitel:%d%d%d%d%d%d%d%d %d+$") then
        local phoneNumber, unitAmount = string.match(data, "^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
        unitAmount = tonumber(unitAmount)
        if unitAmount and unitAmount <= 2000 then
            SmsModule.sendSms("1444", phoneNumber .. " " .. unitAmount)
        else
            SmsModule.sendSms(num, "dugaar 8 orontoi, Negj 2000 aas baga baih. ihdee 5 udaa")
        end
        return
    end

    -- Check if sender is authorized
    if num ~= vars.phone_number1 and num ~= vars.phone_number2 and num ~= vars.phone_number3 then
        -- Don't reply to automatic operator messages (phone numbers less than 8 digits)
        if string.len(num) >= 8 then
            SmsModule.sendSms(num, "taniulaagvi dugaar!")
            log.info("SmsModule", "Unauthorized SMS received from: " .. num)
        else
            log.info("SmsModule", "Automatic operator message received from: " .. num .. ", ignoring without reply")
        end
        return
    end

    -- Store SMS data for processing by main loop
    vars.sms_data = data
    vars.callback_number = num
    log.info("SmsModule", "SMS data stored for processing: " .. data)
end

-- Function to check if SMS functionality is available
function SmsModule.isAvailable()
    return sms ~= nil
end

return SmsModule
